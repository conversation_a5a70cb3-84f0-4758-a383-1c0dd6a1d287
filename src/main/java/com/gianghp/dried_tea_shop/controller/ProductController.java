package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiPageResponse;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.service.ProductService;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/products")
@RequiredArgsConstructor
@Slf4j
public class ProductController {

  private final ProductService productService;

  @GetMapping
  public ResponseEntity<ApiPageResponse<ProductResponseDto>> getAllProducts(
      @RequestParam(required = false) String name,
      @RequestParam(required = false) String categoryId,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) BigDecimal minPrice,
      @RequestParam(required = false) BigDecimal maxPrice,
      @RequestParam(required = false) BigDecimal minRating,
      @RequestParam(required = false, defaultValue = "0") Integer page,
      @RequestParam(required = false, defaultValue = "10") Integer size,
      @RequestParam(required = false, defaultValue = "createdAt") String sortBy
  ) {
    try {
      Page<ProductResponseDto> products = productService.getAllProducts(
          name, categoryId, status, minPrice, maxPrice, minRating, page, size, sortBy
      );
      return ResponseEntity.ok(ApiPageResponse.success(
          products, "Products retrieved successfully"
      ));
    } catch (Exception e) {
      log.error("Error occurred: {}", e.getMessage(), e);
      return ResponseEntity.badRequest()
          .body(ApiPageResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
    }
  }

  // TODO: Implement product REST endpoints
  // GET /api/products/{id} - Get product by ID
  // POST /api/products - Create new product (admin only)
  // PUT /api/products/{id} - Update product (admin only)
  // DELETE /api/products/{id} - Delete product (admin only)
  // GET /api/products/search - Search products
  // GET /api/products/category/{categoryId} - Get products by category
  // GET /api/products/featured - Get featured products
  // GET /api/products/top-rated - Get top rated products
  // GET /api/products/most-reviewed - Get most reviewed products
  // GET /api/products/on-sale - Get products with discounts
  // PUT /api/products/{id}/stock - Update product stock (admin only)
  // PUT /api/products/{id}/discount - Apply discount to product (admin only)
  // DELETE /api/products/{id}/discount - Remove discount from product (admin only)
}
