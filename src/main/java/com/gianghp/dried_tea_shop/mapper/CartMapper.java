package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.cart.AddCartItemRequestDto;
import com.gianghp.dried_tea_shop.dto.cart.AddCartItemResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.CartItemDto;
import com.gianghp.dried_tea_shop.dto.cart.CartProductDto;
import com.gianghp.dried_tea_shop.dto.cart.CartResponseDto;
import com.gianghp.dried_tea_shop.dto.cart.UpdateCartItemRequestDto;
import com.gianghp.dried_tea_shop.entity.Cart;
import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.entity.Product;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {ImageMapper.class, UuidMapper.class})
public interface CartMapper {

    @Mapping(source = "id", target = "cartId", qualifiedByName = "uuidToString")
    @Mapping(source = "user.id", target = "userId", qualifiedByName = "uuidToString")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    CartResponseDto toResponseDto(Cart cart);

    @Mapping(source = "id", target = "cartItemId", qualifiedByName = "uuidToString")
    @Mapping(source = "cart.id", target = "cartId", qualifiedByName = "uuidToString")
    @Mapping(source = "product.id", target = "productId", qualifiedByName = "uuidToString")
    @Mapping(source = "product", target = "product")
    CartItemDto toCartItemDto(CartItem cartItem);

    @Mapping(source = "id", target = "productId", qualifiedByName = "uuidToString")
    @Mapping(source = "image", target = "image")
    CartProductDto toCartProductDto(Product product);

    @Mapping(source = "id", target = "cartItemId", qualifiedByName = "uuidToString")
    @Mapping(source = "cart.id", target = "cartId", qualifiedByName = "uuidToString")
    @Mapping(source = "product.id", target = "productId", qualifiedByName = "uuidToString")
    AddCartItemResponseDto toAddCartItemResponseDto(CartItem cartItem);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "cart", ignore = true)
    @Mapping(target = "product", ignore = true)
    CartItem toEntity(AddCartItemRequestDto addCartItemRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "cart", ignore = true)
    @Mapping(target = "product", ignore = true)
    void updateEntityFromDto(UpdateCartItemRequestDto updateCartItemRequestDto, @MappingTarget CartItem cartItem);
}
