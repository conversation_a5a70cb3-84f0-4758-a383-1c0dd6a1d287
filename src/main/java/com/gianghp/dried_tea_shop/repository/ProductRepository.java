package com.gianghp.dried_tea_shop.repository;

import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.enums.ProductStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Repository
public interface ProductRepository extends JpaRepository<Product, UUID>,
    JpaSpecificationExecutor<Product> {
    
    /**
     * Find products by status
     */
    List<Product> findByStatus(ProductStatus status);
    
    /**
     * Find products by status with pagination
     */
    Page<Product> findByStatus(ProductStatus status, Pageable pageable);
    
    /**
     * Find products by category
     */
    List<Product> findByCategoryId(UUID categoryId);
    
    /**
     * Find products by category with pagination
     */
    Page<Product> findByCategoryId(UUID categoryId, Pageable pageable);
    
    /**
     * Find products by category and status
     */
    List<Product> findByCategoryIdAndStatus(UUID categoryId, ProductStatus status);
    
    /**
     * Find products by name containing (case insensitive)
     */
    List<Product> findByNameContainingIgnoreCase(String name);
    
    /**
     * Find products by name containing and status
     */
    List<Product> findByNameContainingIgnoreCaseAndStatus(String name, ProductStatus status);
    
    /**
     * Find products by price range
     */
    List<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);
    
    /**
     * Find products by price range and status
     */
    List<Product> findByPriceBetweenAndStatus(BigDecimal minPrice, BigDecimal maxPrice, ProductStatus status);
    
    /**
     * Find products with stock quantity greater than
     */
    List<Product> findByStockQuantityGreaterThan(Integer quantity);
    
    /**
     * Find products with low stock (less than specified quantity)
     */
    List<Product> findByStockQuantityLessThan(Integer quantity);
    
    /**
     * Find products with no stock
     */
    List<Product> findByStockQuantity(Integer quantity);
    
    /**
     * Find products by rating greater than or equal
     */
    List<Product> findByRatingGreaterThanEqual(BigDecimal rating);
    
    /**
     * Find products with discount
     */
    @Query("SELECT p FROM Product p WHERE p.discount IS NOT NULL")
    List<Product> findProductsWithDiscount();
    
    /**
     * Find products without discount
     */
    @Query("SELECT p FROM Product p WHERE p.discount IS NULL")
    List<Product> findProductsWithoutDiscount();
    
    /**
     * Find top rated products
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status ORDER BY p.rating DESC, p.reviewCount DESC")
    List<Product> findTopRatedProducts(@Param("status") ProductStatus status, Pageable pageable);
    
    /**
     * Find most reviewed products
     */
    @Query("SELECT p FROM Product p WHERE p.status = :status ORDER BY p.reviewCount DESC, p.rating DESC")
    List<Product> findMostReviewedProducts(@Param("status") ProductStatus status, Pageable pageable);
    
    /**
     * Search products by keyword in name or description
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) " +
           "AND p.status = :status")
    List<Product> searchProducts(@Param("keyword") String keyword, @Param("status") ProductStatus status);
    
    /**
     * Find products by category name
     */
    @Query("SELECT p FROM Product p WHERE p.category.name = :categoryName AND p.status = :status")
    List<Product> findByCategoryNameAndStatus(@Param("categoryName") String categoryName, @Param("status") ProductStatus status);
    
    /**
     * Count products by status
     */
    long countByStatus(ProductStatus status);
    
    /**
     * Count products by category
     */
    long countByCategoryId(UUID categoryId);
}
